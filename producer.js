// Producer for PGMQ, sends messages to the queue for benchmarking
const { Pgmq } = require('pgmq-js');
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

function logWithTimestamp(message) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  console.log(`[${timestamp}] ${message}`);
}

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433', 10),
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: false,
};

// Function to generate payload of specified size in KB
function generatePayload(sizeInKB) {
  const baseChar = 'x';
  const bytesPerChar = 1; // Approximation for ASCII
  const totalChars = sizeInKB * 1024 / bytesPerChar;
  return baseChar.repeat(totalChars);
}

// Setup CSV file for producer metrics
function setupCsvFile() {
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace('T', '_').split('.')[0];
  const fileName = `producer_metrics_${timestamp}.csv`;
  const filePath = path.join(__dirname, fileName);
  
  // Create CSV header
  fs.writeFileSync(filePath, 'message_id,size_kb,sent_timestamp\n');
  
  return filePath;
}

async function setupPgmq() {
  logWithTimestamp('Menyiapkan ekstensi PGMQ...');
  const client = new Client(dbConfig);
  try {
    await client.connect();
    await client.query('CREATE EXTENSION IF NOT EXISTS pgmq');
    await client.query('CREATE SCHEMA IF NOT EXISTS pgmq');
  } catch (error) {
    logWithTimestamp(`Error saat menyiapkan PGMQ: ${error.message}`);
    throw error;
  } finally {
    await client.end();
  }
}

async function runProducer() {
  await setupPgmq();
  const queueName = process.env.QUEUE_NAME || 'pgmq_test';
  const totalMessages = parseInt(process.env.TOTAL_MESSAGES || '10000', 10);
  const messageSizeKB = parseInt(process.env.MESSAGE_SIZE_KB || '1', 10);
  
  // Generate payload of specified size
  const payload = generatePayload(messageSizeKB);
  logWithTimestamp(`Ukuran pesan: ${messageSizeKB} KB`);
  
  // Setup CSV file
  const csvFilePath = setupCsvFile();
  logWithTimestamp(`Menyimpan data ke: ${csvFilePath}`);
  
  let sent = 0;
  let pgmq;
  
  try {
    logWithTimestamp('Menghubungkan ke PostgreSQL...');
    pgmq = await Pgmq.new(dbConfig);
    await pgmq.queue.create(queueName).catch(() => {});
    logWithTimestamp(`Mengirim ${totalMessages} pesan...`);
    
    const startTime = Date.now();
    
    // Send messages individually, one at a time
    for (let i = 0; i < totalMessages; i++) {
      const sendTime = Date.now();
      const message = { 
        id: i, 
        sendTime, 
        data: `Test message ${i}`,
        payload: payload
      };
      
      try {
        await pgmq.msg.send(queueName, message);
        sent++;
        
        // Append to CSV file immediately after each message
        const csvLine = `${i},${messageSizeKB},${sendTime}\n`;
        fs.appendFileSync(csvFilePath, csvLine);
        
        // Log progress periodically
        if (sent % 100 === 0) {
          const currentTime = Date.now();
          const rate = sent / ((currentTime - startTime) / 1000);
          logWithTimestamp(`${sent} pesan telah dikirim (${rate.toFixed(2)} pesan/detik)`);
        }
      } catch (err) {
        logWithTimestamp(`Error saat mengirim pesan ke-${i}: ${err.message}`);
      }
    }
    
    const finalTime = Date.now();
    const finalRate = sent / ((finalTime - startTime) / 1000);
    logWithTimestamp(`Total: ${sent} pesan telah dikirim (${finalRate.toFixed(2)} pesan/detik)`);
    logWithTimestamp('Producer selesai');
  } catch (error) {
    logWithTimestamp(`Error producer: ${error.message}`);
  } finally {
    if (pgmq) await pgmq.close();
  }
}

runProducer();
