{"name": "pg-boss", "version": "10.2.0", "description": "Queueing jobs in Postgres from Node.js like a boss", "main": "./src/index.js", "engines": {"node": ">=20"}, "dependencies": {"cron-parser": "^4.9.0", "pg": "^8.14.1", "serialize-error": "^8.1.0"}, "devDependencies": {"@types/node": "^20.17.30", "luxon": "^3.6.1", "mocha": "^10.8.2", "nyc": "^17.1.0", "standard": "^17.1.2"}, "scripts": {"test": "standard && mocha", "cover": "nyc npm test", "tsc": "tsc --noEmit types.d.ts", "readme": "node ./test/readme.js", "db:migrate": "node -e 'console.log(require(\"./src\").getMigrationPlans())'", "db:construct": "node -e 'console.log(require(\"./src\").getConstructionPlans())'"}, "mocha": {"timeout": 10000, "slow": 10000, "bail": true, "parallel": true, "require": "./test/hooks"}, "nyc": {"include": ["src/**/*.js"], "sourceMap": false, "instrument": true, "reporter": ["lcov", "text-summary", "text"]}, "standard": {"globals": ["describe", "it", "before", "after", "before", "beforeEach", "after<PERSON>ach"]}, "repository": {"type": "git", "url": "git+https://github.com/timgit/pg-boss.git"}, "keywords": ["postgresql", "postgres", "queue", "job"], "author": "timgit", "license": "MIT", "bugs": {"url": "https://github.com/timgit/pg-boss/issues"}, "homepage": "https://github.com/timgit/pg-boss#readme", "types": "./types.d.ts"}