#!/usr/bin/env node

// Setup script for PGMQ Benchmark System
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const config = require('./config/benchmark-config');

class SetupManager {
  constructor() {
    this.steps = [
      'checkDirectories',
      'checkPostgreSQL', 
      'setupPGMQ',
      'validateConfig',
      'runSampleTest'
    ];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  async checkDirectories() {
    this.log('Checking directory structure...');
    
    const dirs = [
      config.data.sharedDir,
      config.data.resultsDir,
      config.data.logsDir,
      'workers',
      'lib',
      'dashboard/public'
    ];

    for (const dir of dirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        this.log(`Created directory: ${dir}`);
      }
    }
    
    this.log('Directory structure verified', 'success');
  }

  async checkPostgreSQL() {
    this.log('Checking PostgreSQL connection...');
    
    const { Client } = require('pg');
    const client = new Client(config.database);
    
    try {
      await client.connect();
      const result = await client.query('SELECT version()');
      this.log(`PostgreSQL connected: ${result.rows[0].version}`, 'success');
      await client.end();
    } catch (error) {
      this.log(`PostgreSQL connection failed: ${error.message}`, 'error');
      this.log('Please ensure PostgreSQL is running and connection details are correct');
      throw error;
    }
  }

  async setupPGMQ() {
    this.log('Setting up PGMQ extension...');
    
    const { Client } = require('pg');
    const client = new Client(config.database);
    
    try {
      await client.connect();
      
      // Create extension
      await client.query('CREATE EXTENSION IF NOT EXISTS pgmq');
      this.log('PGMQ extension created');
      
      // Create schema
      await client.query('CREATE SCHEMA IF NOT EXISTS pgmq');
      this.log('PGMQ schema created');
      
      // Test queue creation
      const { Pgmq } = require('pgmq-js');
      const pgmq = await Pgmq.new(config.database);
      await pgmq.queue.create('setup_test_queue');
      await pgmq.queue.drop('setup_test_queue');
      await pgmq.close();
      
      this.log('PGMQ setup completed successfully', 'success');
      await client.end();
    } catch (error) {
      this.log(`PGMQ setup failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async validateConfig() {
    this.log('Validating configuration...');
    
    const validation = config.validate();
    if (!validation.isValid) {
      this.log('Configuration validation failed:', 'error');
      validation.errors.forEach(error => this.log(`  - ${error}`, 'error'));
      throw new Error('Invalid configuration');
    }
    
    this.log('Configuration validated', 'success');
    this.log(`Test parameters: ${config.benchmark.totalMessages} messages, ${config.benchmark.messageSizeKB}KB each`);
    this.log(`Cluster config: ${config.benchmark.producerInstances} producers, ${config.benchmark.consumerInstances} consumers`);
  }

  async runSampleTest() {
    this.log('Running sample benchmark test...');
    
    try {
      const BenchmarkRunner = require('./benchmark-runner');
      const runner = new BenchmarkRunner();
      
      // Run a small test
      const originalMessages = config.benchmark.totalMessages;
      config.benchmark.totalMessages = 100; // Small test
      
      const result = await runner.runSingleBenchmark(100, 1, 1, 1);
      
      // Restore original config
      config.benchmark.totalMessages = originalMessages;
      
      this.log('Sample test completed successfully', 'success');
      this.log(`Producer throughput: ${result.producerThroughput} msg/sec`);
      this.log(`Consumer throughput: ${result.consumerThroughput} msg/sec`);
      this.log(`Average latency: ${result.avgLatencyMs} ms`);
      
    } catch (error) {
      this.log(`Sample test failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async run() {
    this.log('🚀 Starting PGMQ Benchmark System Setup');
    this.log(`PostgreSQL Version: ${config.postgresql.description}`);
    
    try {
      for (const step of this.steps) {
        await this[step]();
      }
      
      this.log('🎉 Setup completed successfully!', 'success');
      this.log('');
      this.log('Next steps:');
      this.log('1. Run benchmark: npm run benchmark');
      this.log('2. Start dashboard: npm run dashboard');
      this.log('3. Run stress test: npm run stress-test');
      this.log('4. View results in: shared-data/results/');
      
    } catch (error) {
      this.log('❌ Setup failed. Please check the error messages above.', 'error');
      process.exit(1);
    }
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new SetupManager();
  setup.run();
}

module.exports = SetupManager;
