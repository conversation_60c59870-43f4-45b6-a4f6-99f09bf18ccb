#!/usr/bin/env node

// Demo script to showcase PGMQ Benchmark System capabilities
const BenchmarkRunner = require('./benchmark-runner');
const StressTestRunner = require('./stress-test');
const DashboardServer = require('./dashboard/server');
const config = require('./config/benchmark-config');

class DemoRunner {
  constructor() {
    this.dashboard = null;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : '🔍';
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  async startDashboard() {
    this.log('Starting web dashboard...');
    this.dashboard = new DashboardServer();
    
    return new Promise((resolve) => {
      this.dashboard.server.listen(config.dashboard.port, () => {
        this.log(`Dashboard available at: http://localhost:${config.dashboard.port}`, 'success');
        this.dashboard.startMonitoring();
        resolve();
      });
    });
  }

  async runBasicBenchmark() {
    this.log('Running basic benchmark test...');
    
    const runner = new BenchmarkRunner();
    const result = await runner.runSingleBenchmark(
      1000,  // 1K messages
      1,     // 1KB each
      2,     // 2 producers
      2      // 2 consumers
    );
    
    this.log('Basic benchmark completed', 'success');
    this.log(`Producer: ${result.producerThroughput} msg/sec`);
    this.log(`Consumer: ${result.consumerThroughput} msg/sec`);
    this.log(`Latency: ${result.avgLatencyMs} ms`);
    
    return result;
  }

  async runScalabilityTest() {
    this.log('Running scalability test...');
    
    const messageCounts = [500, 1000, 2000, 5000];
    const results = [];
    
    for (const count of messageCounts) {
      this.log(`Testing ${count} messages...`);
      
      const runner = new BenchmarkRunner();
      const result = await runner.runSingleBenchmark(count, 1, 2, 2);
      results.push(result);
      
      this.log(`${count} messages: ${result.consumerThroughput} msg/sec`);
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    this.log('Scalability test completed', 'success');
    return results;
  }

  async runMessageSizeTest() {
    this.log('Running message size test...');
    
    const messageSizes = [1, 10, 50, 100]; // KB
    const results = [];
    
    for (const size of messageSizes) {
      this.log(`Testing ${size}KB messages...`);
      
      const runner = new BenchmarkRunner();
      const result = await runner.runSingleBenchmark(1000, size, 2, 2);
      results.push(result);
      
      this.log(`${size}KB: ${result.consumerThroughput} msg/sec`);
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    this.log('Message size test completed', 'success');
    return results;
  }

  async runConcurrencyTest() {
    this.log('Running concurrency test...');
    
    const configs = [
      { producers: 1, consumers: 1 },
      { producers: 2, consumers: 2 },
      { producers: 4, consumers: 2 },
      { producers: 2, consumers: 4 }
    ];
    
    const results = [];
    
    for (const cfg of configs) {
      this.log(`Testing ${cfg.producers}p/${cfg.consumers}c...`);
      
      const runner = new BenchmarkRunner();
      const result = await runner.runSingleBenchmark(2000, 1, cfg.producers, cfg.consumers);
      results.push({ ...result, config: cfg });
      
      this.log(`${cfg.producers}p/${cfg.consumers}c: ${result.consumerThroughput} msg/sec`);
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    this.log('Concurrency test completed', 'success');
    return results;
  }

  generateSummaryReport(basicResult, scalabilityResults, sizeResults, concurrencyResults) {
    const report = `
PGMQ Benchmark Demo Results
===========================
Timestamp: ${new Date().toISOString()}
PostgreSQL: ${config.postgresql.description}

BASIC BENCHMARK:
- Producer Throughput: ${basicResult.producerThroughput} msg/sec
- Consumer Throughput: ${basicResult.consumerThroughput} msg/sec
- Average Latency: ${basicResult.avgLatencyMs} ms

SCALABILITY TEST:
${scalabilityResults.map(r => 
  `- ${r.totalMessages} messages: ${r.consumerThroughput} msg/sec`
).join('\n')}

MESSAGE SIZE TEST:
${sizeResults.map(r => 
  `- ${r.messageSizeKB}KB: ${r.consumerThroughput} msg/sec`
).join('\n')}

CONCURRENCY TEST:
${concurrencyResults.map(r => 
  `- ${r.config.producers}p/${r.config.consumers}c: ${r.consumerThroughput} msg/sec`
).join('\n')}

BEST PERFORMANCE:
- Highest Throughput: ${Math.max(...concurrencyResults.map(r => parseFloat(r.consumerThroughput)))} msg/sec
- Lowest Latency: ${Math.min(...sizeResults.map(r => parseFloat(r.avgLatencyMs)))} ms

DATA LOCATION:
- CSV Results: shared-data/results/
- Logs: shared-data/logs/
- Dashboard: http://localhost:${config.dashboard.port}
`;

    const reportPath = `shared-data/results/demo_report_${config.getTestId()}.txt`;
    require('fs').writeFileSync(reportPath, report);
    
    this.log(`Demo report saved to: ${reportPath}`, 'success');
    return report;
  }

  async run() {
    this.log('🚀 Starting PGMQ Benchmark Demo');
    this.log('This demo will showcase all system capabilities...');
    
    try {
      // Start dashboard
      await this.startDashboard();
      
      this.log('Demo will run for approximately 5-10 minutes...');
      this.log('You can monitor progress at: http://localhost:3000');
      
      // Wait for dashboard to be ready
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Run tests
      const basicResult = await this.runBasicBenchmark();
      const scalabilityResults = await this.runScalabilityTest();
      const sizeResults = await this.runMessageSizeTest();
      const concurrencyResults = await this.runConcurrencyTest();
      
      // Generate report
      const report = this.generateSummaryReport(
        basicResult, 
        scalabilityResults, 
        sizeResults, 
        concurrencyResults
      );
      
      this.log('🎉 Demo completed successfully!', 'success');
      this.log('');
      this.log('Summary:');
      console.log(report);
      
      this.log('Dashboard is still running at: http://localhost:3000');
      this.log('Press Ctrl+C to stop the dashboard');
      
    } catch (error) {
      this.log(`Demo failed: ${error.message}`, 'error');
      process.exit(1);
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Demo stopped by user');
  process.exit(0);
});

// Run demo if called directly
if (require.main === module) {
  const demo = new DemoRunner();
  demo.run();
}

module.exports = DemoRunner;
