// Consumer for PGMQ, reads and processes messages from the queue for benchmarking
const { Pgmq } = require('pgmq-js');
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

function logWithTimestamp(message) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  console.log(`[${timestamp}] ${message}`);
}

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433', 10),
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: false,
};

// Setup CSV file for consumer metrics
function setupCsvFile() {
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace('T', '_').split('.')[0];
  const fileName = `consumer_metrics_${timestamp}.csv`;
  const filePath = path.join(__dirname, fileName);
  
  // Create CSV header
  fs.writeFileSync(filePath, 'message_id,size_kb,sent_timestamp,received_timestamp,latency_ms\n');
  
  return filePath;
}

async function setupPgmq() {
  logWithTimestamp('Menyiapkan ekstensi PGMQ...');
  const client = new Client(dbConfig);
  try {
    await client.connect();
    await client.query('CREATE EXTENSION IF NOT EXISTS pgmq');
    await client.query('CREATE SCHEMA IF NOT EXISTS pgmq');
  } catch (error) {
    logWithTimestamp(`Error saat menyiapkan PGMQ: ${error.message}`);
    throw error;
  } finally {
    await client.end();
  }
}

async function runConsumer() {
  await setupPgmq();
  const queueName = process.env.QUEUE_NAME || 'pgmq_test';
  const totalMessages = parseInt(process.env.TOTAL_MESSAGES || '10000', 10);
  
  // Setup CSV file
  const csvFilePath = setupCsvFile();
  logWithTimestamp(`Menyimpan data ke: ${csvFilePath}`);
  
  let processed = 0;
  let pgmq;
  let csvData = '';
  
  try {
    logWithTimestamp('Menghubungkan ke PostgreSQL...');
    pgmq = await Pgmq.new(dbConfig);
    let startTime = Date.now();
    
    while (processed < totalMessages) {
      try {
        const msg = await pgmq.msg.read(queueName, 30);
        if (msg) {
          const receiveTime = Date.now();
          const messageContent = msg.message;
          
          // Calculate message size from payload
          const messageSizeKB = messageContent.payload ? 
            Math.round(messageContent.payload.length / 1024) : 0;
          
          // Calculate latency
          const latencyMs = receiveTime - messageContent.sendTime;
          
          // Append to CSV data
          csvData += `${messageContent.id},${messageSizeKB},${messageContent.sendTime},${receiveTime},${latencyMs}\n`;
          
          await pgmq.msg.archive(queueName, msg.msgId);
          processed++;
          
          // Periodically write to file to avoid memory issues with large datasets
          if (processed % 100 === 0) {
            fs.appendFileSync(csvFilePath, csvData);
            csvData = '';
          }
          
          if (processed % 1000 === 0 || processed === totalMessages) {
            logWithTimestamp(`${processed} pesan telah diproses`);
          }
        } else {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        logWithTimestamp(`Error consumer: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    // Write any remaining data
    if (csvData) {
      fs.appendFileSync(csvFilePath, csvData);
    }
    
    const endTime = Date.now();
    const throughput = totalMessages / ((endTime - startTime) / 1000);
    logWithTimestamp(`Throughput Consumer: ${throughput.toFixed(2)} pesan/detik`);
    logWithTimestamp('Consumer selesai');
  } catch (error) {
    logWithTimestamp(`Error consumer: ${error.message}`);
  } finally {
    if (pgmq) await pgmq.close();
  }
}

runConsumer();
